-- Script untuk menambahkan menu Barang Masuk
-- Jalankan script ini setelah tabel barang_masuk dibuat

-- Insert menu Barang Masuk
INSERT INTO tbl_menu (nama_menu, link, icon, urutan, is_active) 
VALUES ('Barang Masuk', 'barangmasuk', 'fas fa-truck', 8, 'Y');

-- Dapatkan ID menu yang baru saja diinsert
SET @menu_id = LAST_INSERT_ID();

-- Insert akses menu untuk semua level user yang ada
INSERT INTO tbl_akses_menu (id_menu, id_level, view)
SELECT @menu_id, id_level, 'Y' FROM tbl_userlevel;

-- <PERSON><PERSON><PERSON><PERSON> hasil
SELECT 'Menu Barang Masuk berhasil ditambahkan dengan ID:' as status, @menu_id as menu_id;
