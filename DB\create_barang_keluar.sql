-- <PERSON>ript untuk membuat tabel barang keluar
-- Jalankan script ini setelah tabel stok_movement dan stok_barang dibuat

-- Tabel header barang keluar
CREATE TABLE barang_keluar (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    nomor_pengeluaran VARCHAR(50) NOT NULL UNIQUE,
    tanggal DATE NOT NULL,
    id_pelanggan INT,
    jenis <PERSON>('penjualan', 'retur_pembelian', 'transfer_keluar', 'penyesuaian', 'produksi', 'rusak', 'hilang', 'sample') NOT NULL,
    ref_nomor VARCHAR(50), -- <PERSON><PERSON><PERSON><PERSON> dokumen (SO, Transfer, dll)
    keterangan TEXT,
    status ENUM('draft', 'final') DEFAULT 'draft',
    total_item INT DEFAULT 0,
    total_qty DECIMAL(15,2) DEFAULT 0,
    created_by INT,
    finalized_by INT,
    finalized_at DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (id_pelanggan) REFERENCES pelanggan(id)
) ENGINE=InnoDB;

-- Tabel detail barang keluar
CREATE TABLE barang_keluar_detail (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    id_barang_keluar BIGINT UNSIGNED NOT NULL,
    id_barang INT NOT NULL,
    id_gudang INT NOT NULL,
    qty_keluar DECIMAL(12,2) NOT NULL DEFAULT 0,
    id_satuan INT,
    harga_satuan DECIMAL(15,2) DEFAULT 0,
    total_harga DECIMAL(15,2) GENERATED ALWAYS AS (qty_keluar * harga_satuan) STORED,
    keterangan TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (id_barang_keluar) REFERENCES barang_keluar(id) ON DELETE CASCADE,
    FOREIGN KEY (id_barang) REFERENCES barang(id),
    FOREIGN KEY (id_gudang) REFERENCES gudang(id),
    FOREIGN KEY (id_satuan) REFERENCES satuan(id)
) ENGINE=InnoDB;

-- Indexes untuk optimasi performa
CREATE INDEX idx_barang_keluar_tanggal ON barang_keluar(tanggal);
CREATE INDEX idx_barang_keluar_jenis ON barang_keluar(jenis);
CREATE INDEX idx_barang_keluar_status ON barang_keluar(status);
CREATE INDEX idx_barang_keluar_nomor ON barang_keluar(nomor_pengeluaran);
CREATE INDEX idx_barang_keluar_detail_barang ON barang_keluar_detail(id_barang);
CREATE INDEX idx_barang_keluar_detail_gudang ON barang_keluar_detail(id_gudang);

-- Trigger untuk update summary di header saat detail berubah
DELIMITER $$
CREATE TRIGGER trg_update_barang_keluar_summary_insert
AFTER INSERT ON barang_keluar_detail
FOR EACH ROW
BEGIN
    UPDATE barang_keluar 
    SET 
        total_item = (
            SELECT COUNT(*) 
            FROM barang_keluar_detail 
            WHERE id_barang_keluar = NEW.id_barang_keluar
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_keluar), 0) 
            FROM barang_keluar_detail 
            WHERE id_barang_keluar = NEW.id_barang_keluar
        )
    WHERE id = NEW.id_barang_keluar;
END$$

CREATE TRIGGER trg_update_barang_keluar_summary_update
AFTER UPDATE ON barang_keluar_detail
FOR EACH ROW
BEGIN
    UPDATE barang_keluar 
    SET 
        total_item = (
            SELECT COUNT(*) 
            FROM barang_keluar_detail 
            WHERE id_barang_keluar = NEW.id_barang_keluar
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_keluar), 0) 
            FROM barang_keluar_detail 
            WHERE id_barang_keluar = NEW.id_barang_keluar
        )
    WHERE id = NEW.id_barang_keluar;
END$$

CREATE TRIGGER trg_update_barang_keluar_summary_delete
AFTER DELETE ON barang_keluar_detail
FOR EACH ROW
BEGIN
    UPDATE barang_keluar 
    SET 
        total_item = (
            SELECT COUNT(*) 
            FROM barang_keluar_detail 
            WHERE id_barang_keluar = OLD.id_barang_keluar
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_keluar), 0) 
            FROM barang_keluar_detail 
            WHERE id_barang_keluar = OLD.id_barang_keluar
        )
    WHERE id = OLD.id_barang_keluar;
END$$
DELIMITER ;

-- Trigger untuk finalisasi barang keluar ke stok_movement
DELIMITER $$
CREATE TRIGGER trg_finalize_barang_keluar
AFTER UPDATE ON barang_keluar
FOR EACH ROW
BEGIN
    -- Hanya jalankan jika status berubah dari draft ke final
    IF OLD.status = 'draft' AND NEW.status = 'final' THEN
        -- Insert semua detail barang keluar ke stok_movement
        INSERT INTO stok_movement (
            tanggal, id_barang, id_gudang, tipe_transaksi,
            qty_in, qty_out, keterangan, ref_transaksi, user_input
        )
        SELECT
            CONCAT(NEW.tanggal, ' ', TIME(NOW())),
            bkd.id_barang,
            bkd.id_gudang,
            CASE NEW.jenis
                WHEN 'penjualan' THEN 'penjualan'
                WHEN 'retur_pembelian' THEN 'retur_beli'
                WHEN 'transfer_keluar' THEN 'transfer_keluar'
                ELSE 'penyesuaian'
            END,
            0, -- qty_in
            bkd.qty_keluar, -- qty_out
            CONCAT('Barang Keluar: ', COALESCE(bkd.keterangan, NEW.keterangan, '')),
            NEW.nomor_pengeluaran,
            CONCAT('user_', COALESCE(NEW.finalized_by, NEW.created_by, 0))
        FROM barang_keluar_detail bkd
        WHERE bkd.id_barang_keluar = NEW.id;
    END IF;
END$$
DELIMITER ;

-- Views untuk reporting dan kemudahan query
CREATE OR REPLACE VIEW v_barang_keluar_summary AS
SELECT
    bk.id,
    bk.nomor_pengeluaran,
    bk.tanggal,
    bk.jenis,
    bk.ref_nomor,
    bk.status,
    bk.total_item,
    bk.total_qty,
    p.kode as kode_pelanggan,
    p.nama as nama_pelanggan,
    bk.keterangan,
    bk.created_by,
    bk.finalized_by,
    bk.finalized_at,
    bk.created_at,
    bk.updated_at
FROM barang_keluar bk
LEFT JOIN pelanggan p ON bk.id_pelanggan = p.id
ORDER BY bk.tanggal DESC, bk.id DESC;

CREATE OR REPLACE VIEW v_barang_keluar_detail AS
SELECT
    bkd.id,
    bkd.id_barang_keluar,
    bk.nomor_pengeluaran,
    bk.tanggal,
    bk.jenis,
    bk.status,
    b.kode_barang,
    b.nama_barang,
    g.kode_gudang,
    g.nama_gudang,
    bkd.qty_keluar,
    s.kode_satuan,
    s.nama_satuan,
    bkd.harga_satuan,
    bkd.total_harga,
    bkd.keterangan,
    bkd.created_at
FROM barang_keluar_detail bkd
JOIN barang_keluar bk ON bkd.id_barang_keluar = bk.id
JOIN barang b ON bkd.id_barang = b.id
JOIN gudang g ON bkd.id_gudang = g.id
LEFT JOIN satuan s ON bkd.id_satuan = s.id
ORDER BY bk.tanggal DESC, b.nama_barang ASC;
