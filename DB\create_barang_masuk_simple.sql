-- <PERSON><PERSON>t sederhana untuk membuat tabel barang masuk

-- Tabel header barang masuk
CREATE TABLE barang_masuk (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    nomor_penerimaan VARCHAR(50) NOT NULL UNIQUE,
    tanggal DATE NOT NULL,
    id_supplier INT,
    j<PERSON><PERSON>('pembelian', 'retur_penjualan', 'bonus', 'titipan', 'penyesuaian', 'produksi', 'transfer_masuk') NOT NULL,
    ref_nomor VARCHAR(50),
    keterangan TEXT,
    status ENUM('draft', 'final') DEFAULT 'draft',
    total_item INT DEFAULT 0,
    total_qty DECIMAL(15,2) DEFAULT 0,
    created_by INT,
    finalized_by INT,
    finalized_at DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (id_supplier) REFERENCES supplier(id) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;
