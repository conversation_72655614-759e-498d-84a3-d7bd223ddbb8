<form action="#" id="form" class="form-horizontal">
    <input type="hidden" value="" name="id" />
    <div class="card-body">

        <!-- Tab Navigation -->
        <ul class="nav nav-tabs" id="barangMasukTab" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="basic-tab" data-toggle="tab" href="#basic" role="tab">Data Dasar</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="supplier-tab" data-toggle="tab" href="#supplier" role="tab">Supplier & Referensi</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="keterangan-tab" data-toggle="tab" href="#keterangan" role="tab">Keterangan</a>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="barangMasukTabContent">

            <!-- Tab Data Dasar -->
            <div class="tab-pane fade show active" id="basic" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="nomor_penerimaan" class="col-sm-3 col-form-label">Nomor Penerimaan</label>
                        <div class="col-sm-9 kosong">
                            <div class="input-group">
                                <input type="text" class="form-control" name="nomor_penerimaan" id="nomor_penerimaan" placeholder="Nomor akan di-generate otomatis" autocomplete="off">
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-outline-secondary" id="btn-generate-nomor" onclick="generateNomor()" title="Generate Nomor">
                                        <i class="fas fa-sync"></i>
                                    </button>
                                </div>
                            </div>
                            <small class="form-text text-muted">Format: BM-2025-001, BM-2025-002, dst. Kosongkan untuk generate otomatis.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="tanggal" class="col-sm-3 col-form-label">Tanggal <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <input type="date" class="form-control" name="tanggal" id="tanggal" required>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="jenis" class="col-sm-3 col-form-label">Jenis <span class="text-danger">*</span></label>
                        <div class="col-sm-9 kosong">
                            <select class="form-control select2" name="jenis" id="jenis" required style="width: 100%;">
                                <option value="">-- Pilih Jenis --</option>
                                <option value="pembelian">Pembelian</option>
                                <option value="retur_penjualan">Retur Penjualan</option>
                                <option value="bonus">Bonus</option>
                                <option value="titipan">Titipan</option>
                                <option value="penyesuaian">Penyesuaian</option>
                                <option value="produksi">Produksi</option>
                                <option value="transfer_masuk">Transfer Masuk</option>
                            </select>
                            <span class="help-block"></span>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Tab Supplier & Referensi -->
            <div class="tab-pane fade" id="supplier" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="id_supplier" class="col-sm-3 col-form-label">Supplier</label>
                        <div class="col-sm-9 kosong">
                            <select class="form-control select2" name="id_supplier" id="id_supplier" style="width: 100%;">
                                <option value="">-- Pilih Supplier --</option>
                                <?php foreach ($supplier_list as $supplier): ?>
                                    <option value="<?= $supplier->id ?>"><?= $supplier->kode ?> - <?= $supplier->nama ?></option>
                                <?php endforeach; ?>
                            </select>
                            <small class="form-text text-muted">Opsional. Pilih supplier jika barang masuk dari pembelian.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label for="ref_nomor" class="col-sm-3 col-form-label">Nomor Referensi</label>
                        <div class="col-sm-9 kosong">
                            <input type="text" class="form-control" name="ref_nomor" id="ref_nomor" placeholder="Nomor PO, Transfer, dll" autocomplete="off">
                            <small class="form-text text-muted">Nomor dokumen referensi seperti PO, Transfer Order, dll.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Tab Keterangan -->
            <div class="tab-pane fade" id="keterangan" role="tabpanel">
                <div class="pt-3">

                    <div class="form-group row">
                        <label for="keterangan" class="col-sm-3 col-form-label">Keterangan</label>
                        <div class="col-sm-9 kosong">
                            <textarea class="form-control" name="keterangan" id="keterangan" placeholder="Keterangan tambahan (opsional)" rows="4"></textarea>
                            <small class="form-text text-muted">Informasi tambahan mengenai penerimaan barang ini.</small>
                            <span class="help-block"></span>
                        </div>
                    </div>

                </div>
            </div>

        </div>

    </div>
</form>

<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        dropdownParent: $('#modal_form'),
        placeholder: "-- Pilih --",
        allowClear: true
    });
    
    // Event handler untuk perubahan jenis
    $('#jenis').on('change', function() {
        var jenis = $(this).val();
        
        // Show/hide supplier field based on jenis
        if (jenis === 'pembelian' || jenis === 'retur_penjualan') {
            $('#id_supplier').closest('.form-group').show();
        } else {
            $('#id_supplier').closest('.form-group').hide();
            $('#id_supplier').val('').trigger('change');
        }
        
        // Update placeholder untuk ref_nomor
        var placeholder = 'Nomor referensi';
        switch(jenis) {
            case 'pembelian':
                placeholder = 'Nomor PO (Purchase Order)';
                break;
            case 'transfer_masuk':
                placeholder = 'Nomor Transfer Order';
                break;
            case 'retur_penjualan':
                placeholder = 'Nomor Invoice Penjualan';
                break;
            case 'produksi':
                placeholder = 'Nomor Work Order';
                break;
            default:
                placeholder = 'Nomor referensi dokumen';
                break;
        }
        $('#ref_nomor').attr('placeholder', placeholder);
    });
    
    // Trigger change event untuk set initial state
    $('#jenis').trigger('change');
});
</script>
