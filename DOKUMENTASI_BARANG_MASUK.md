# Dokumentasi Modul Barang Masuk

## Deskripsi
Modul Barang Masuk adalah sistem untuk mengelola penerimaan barang dari berbagai sumber seperti pembelian, retur penjualan, bonus, transfer, dll. Modul ini terintegrasi penuh dengan sistem stok management untuk otomatisasi update stok setelah finalisasi.

## Fitur Utama
1. **Header-Detail Structure** - Struktur master-detail untuk penerimaan barang
2. **Multi Jenis Penerimaan** - Mendukung berbagai jenis penerimaan barang
3. **Integration with Suppliers** - Integrasi dengan master supplier
4. **Real-time Calculation** - Kalkulasi total otomatis
5. **Draft-Final Workflow** - Status draft untuk editing, final untuk lock
6. **Integration with Stok Movement** - Auto insert ke stok_movement saat finalisasi
7. **Comprehensive Validation** - Validasi lengkap dengan error handling
8. **User-friendly Interface** - Interface yang mudah digunakan

## Struktur Database

### 1. <PERSON><PERSON> barang_masuk (Header)
```sql
CREATE TABLE barang_masuk (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    nomor_penerimaan VARCHAR(50) NOT NULL UNIQUE,
    tanggal DATE NOT NULL,
    id_supplier INT,
    jenis ENUM('pembelian', 'retur_penjualan', 'bonus', 'titipan', 'penyesuaian', 'produksi', 'transfer_masuk') NOT NULL,
    ref_nomor VARCHAR(50),
    keterangan TEXT,
    status ENUM('draft', 'final') DEFAULT 'draft',
    total_item INT DEFAULT 0,
    total_qty DECIMAL(15,2) DEFAULT 0,
    created_by INT,
    finalized_by INT,
    finalized_at DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_supplier) REFERENCES supplier(id)
);
```

### 2. Tabel barang_masuk_detail (Detail)
```sql
CREATE TABLE barang_masuk_detail (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    id_barang_masuk BIGINT UNSIGNED NOT NULL,
    id_barang INT NOT NULL,
    id_gudang INT NOT NULL,
    qty_diterima DECIMAL(12,2) NOT NULL DEFAULT 0,
    id_satuan INT,
    harga_satuan DECIMAL(15,2) DEFAULT 0,
    total_harga DECIMAL(15,2) GENERATED ALWAYS AS (qty_diterima * harga_satuan) STORED,
    keterangan TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_barang_masuk) REFERENCES barang_masuk(id) ON DELETE CASCADE,
    FOREIGN KEY (id_barang) REFERENCES barang(id),
    FOREIGN KEY (id_gudang) REFERENCES gudang(id),
    FOREIGN KEY (id_satuan) REFERENCES satuan(id)
);
```

### 3. Trigger Database
- **trg_update_barang_masuk_summary_***: Update summary di header saat detail berubah
- **trg_finalize_barang_masuk**: Auto insert ke stok_movement saat status berubah ke final

### 4. Views
- **v_barang_masuk_summary**: Summary barang masuk dengan info supplier
- **v_barang_masuk_detail**: Detail barang masuk dengan info lengkap

## Jenis Penerimaan Barang

| Jenis | Deskripsi | Supplier Required | Typical Use Case |
|-------|-----------|-------------------|------------------|
| **pembelian** | Pembelian dari supplier | ✅ | Purchase Order |
| **retur_penjualan** | Retur dari customer | ❌ | Return dari customer |
| **bonus** | Bonus dari supplier | ✅ | Promotional items |
| **titipan** | Barang titipan | ✅ | Consignment |
| **penyesuaian** | Penyesuaian stok | ❌ | Stock adjustment |
| **produksi** | Hasil produksi | ❌ | Manufacturing output |
| **transfer_masuk** | Transfer antar gudang | ❌ | Inter-warehouse transfer |

## File yang Dibuat

### 1. Database
- `DB/create_barang_masuk.sql` - Script tabel, trigger, dan view
- `DB/add_menu_barang_masuk.sql` - Script menu

### 2. Backend
- `application/controllers/BarangMasuk.php` - Controller utama
- `application/models/Mod_barang_masuk.php` - Model database

### 3. Frontend
- `application/views/barang_masuk/barang_masuk.php` - Halaman utama
- `application/views/barang_masuk/form_input.php` - Form header
- `application/views/barang_masuk/detail_barang_masuk.php` - Halaman detail

## Workflow Barang Masuk

### 1. **Create Header (Draft)**
- User input nomor penerimaan (auto-generate), tanggal, jenis
- Pilih supplier (jika diperlukan berdasarkan jenis)
- Input referensi nomor dan keterangan
- Status: DRAFT

### 2. **Manage Detail**
- Add/Edit/Delete item detail
- Auto-load satuan dan harga dari master barang
- Input qty diterima dan harga satuan
- Kalkulasi total harga otomatis

### 3. **Finalisasi**
- Review semua detail
- Finalisasi barang masuk (status → FINAL)
- Auto insert ke stok_movement
- Auto update stok_barang via trigger
- Data tidak dapat diubah lagi

## Fitur Detail

### Auto-Load Data Barang
- Satuan default dari master barang
- Harga beli default dari master barang
- Kalkulasi total harga otomatis

### Real-time Calculation
- Total harga = qty_diterima × harga_satuan
- Summary total item dan total qty
- Update otomatis saat detail berubah

### Integration with Stok Movement
Saat finalisasi, sistem otomatis:
- Insert ke stok_movement dengan tipe sesuai jenis
- qty_in = qty_diterima
- Referensi ke nomor penerimaan

## Method di Model

### Mod_barang_masuk.php

#### Header Methods
```php
// CRUD header
function insert($data)
function update($id, $data)
function get($id)
function delete($id)

// Generate nomor otomatis
function generate_nomor()
function check_nomor_exists($nomor, $id = null)

// Finalisasi
function finalize_barang_masuk($id, $user_final)

// Dropdown data
function get_supplier_dropdown()
function get_barang_dropdown()
function get_gudang_dropdown()
function get_satuan_dropdown()
```

#### Detail Methods
```php
// CRUD detail
function insert_detail($data)
function update_detail($id, $data)
function get_detail($id_barang_masuk)
function delete_detail($id)

// Helper
function get_detail_by_id($id)
function get_barang_detail($id_barang)
```

## Method di Controller

### BarangMasuk.php

#### Header Methods
```php
// CRUD header
public function insert()
public function update()
public function edit($id)
public function delete()

// Finalisasi
public function finalize()
public function generate_nomor()
```

#### Detail Methods
```php
// CRUD detail
public function insert_detail()
public function update_detail()
public function edit_detail($id)
public function delete_detail()

// Helper
public function get_barang_detail()
public function detail($id)
```

## UI/UX Features

### Halaman Utama
- DataTables dengan server-side processing
- Badge jenis dan status dengan warna
- Action buttons berbeda per status
- Summary total item dan qty

### Form Header
- Auto-generate nomor penerimaan
- Multi-tab layout (Data Dasar, Supplier & Referensi, Keterangan)
- Dynamic supplier field berdasarkan jenis
- Date picker untuk tanggal

### Detail Barang Masuk
- Header info dengan summary
- Tabel detail dengan total nilai
- Modal form untuk add/edit item
- Auto-load data barang (satuan, harga)
- Real-time total calculation
- Action buttons per status

## Validasi

### Header Validation
- Nomor penerimaan format BM-YYYY-NNN
- Tanggal wajib diisi
- Jenis wajib dipilih
- Nomor penerimaan unique

### Detail Validation
- Barang wajib dipilih
- Gudang wajib dipilih
- Qty diterima wajib diisi dan positif
- Harga satuan harus positif (jika diisi)

## Status Management

### DRAFT Status
- Data dapat diubah
- Detail dapat di-add/edit/delete
- Dapat difinalisasi
- Dapat dihapus

### FINAL Status
- Data tidak dapat diubah
- Hanya bisa view detail
- Sudah terintegrasi ke stok movement
- Tidak dapat dihapus

## Integration Points

### Dengan Stok Management
- Insert ke stok_movement saat finalisasi
- Update stok_barang via trigger
- Mapping jenis ke tipe transaksi

### Dengan Master Data
- Foreign key ke tabel supplier, barang, gudang, satuan
- Dropdown data dari master yang aktif
- Auto-load data default

## Mapping Jenis ke Tipe Transaksi

| Jenis Barang Masuk | Tipe Stok Movement |
|-------------------|-------------------|
| pembelian | pembelian |
| retur_penjualan | retur_jual |
| transfer_masuk | transfer_masuk |
| bonus | pembelian |
| titipan | pembelian |
| penyesuaian | pembelian |
| produksi | pembelian |

## Error Handling

### Database Level
- Foreign key constraints
- Unique constraints
- Trigger error handling

### Application Level
- Validation dengan pesan detail
- AJAX error handling
- SweetAlert notifications

### UI Level
- Loading states
- Disabled buttons saat process
- Form validation feedback

## Security

### Access Control
- Menu access control
- User level permissions
- Session validation

### Data Integrity
- Foreign key constraints
- Status-based restrictions
- User tracking

## Performance

### Database
- Index pada kolom pencarian
- Efficient queries dengan JOIN
- Server-side DataTables

### Frontend
- AJAX untuk operasi detail
- Lazy loading untuk dropdown
- Minimal DOM manipulation

## Testing Scenarios

### Basic CRUD
1. Create header barang masuk
2. Add detail items
3. Edit detail items
4. Delete detail items
5. Finalize barang masuk

### Jenis Penerimaan
1. Test semua jenis penerimaan
2. Verify supplier field show/hide
3. Test mapping ke stok movement

### Validation
1. Test semua validasi header
2. Test semua validasi detail
3. Test unique constraints
4. Test status restrictions

### Integration
1. Verify auto-load barang data
2. Verify finalisasi ke movement
3. Verify stok update via trigger

## URL Access
- **Halaman utama**: `http://localhost/toko_elektronik/barangmasuk`
- **Form header**: `http://localhost/toko_elektronik/barangmasuk/form_input`
- **Detail barang masuk**: `http://localhost/toko_elektronik/barangmasuk/detail/{id}`

## Future Enhancements

### Reporting
- Laporan barang masuk per periode
- Analisis per supplier
- Export ke Excel/PDF

### Advanced Features
- Batch import dari Excel
- Photo documentation
- Approval workflow
- Email notifications

### Mobile Support
- Mobile-friendly interface
- Barcode scanning
- Offline capability
