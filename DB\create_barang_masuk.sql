-- <PERSON>ript untuk membuat tabel barang masuk
-- Jalankan script ini setelah tabel stok_movement dan stok_barang dibuat

-- Tabel header barang masuk
CREATE TABLE barang_masuk (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    nomor_penerimaan VARCHAR(50) NOT NULL UNIQUE,
    tanggal DATE NOT NULL,
    id_supplier INT,
    jeni<PERSON>('pembelian', 'retur_penjualan', 'bonus', 'titipan', 'penyesuaian', 'produksi', 'transfer_masuk') NOT NULL,
    ref_nomor VARCHAR(50), -- Refer<PERSON><PERSON> dokumen (PO, Transfer, dll)
    keterangan TEXT,
    status ENUM('draft', 'final') DEFAULT 'draft',
    total_item INT DEFAULT 0,
    total_qty DECIMAL(15,2) DEFAULT 0,
    created_by INT,
    finalized_by INT,
    finalized_at DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (id_supplier) REFERENCES supplier(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (created_by) REFERENCES tbl_user(id_user) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (finalized_by) REFERENCES tbl_user(id_user) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Tabel detail barang masuk
CREATE TABLE barang_masuk_detail (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    id_barang_masuk BIGINT UNSIGNED NOT NULL,
    id_barang INT NOT NULL,
    id_gudang INT NOT NULL,
    qty_diterima DECIMAL(12,2) NOT NULL DEFAULT 0,
    id_satuan INT,
    harga_satuan DECIMAL(15,2) DEFAULT 0,
    total_harga DECIMAL(15,2) GENERATED ALWAYS AS (qty_diterima * harga_satuan) STORED,
    keterangan TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (id_barang_masuk) REFERENCES barang_masuk(id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (id_barang) REFERENCES barang(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (id_gudang) REFERENCES gudang(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (id_satuan) REFERENCES satuan(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    UNIQUE KEY unique_barang_masuk_barang_gudang (id_barang_masuk, id_barang, id_gudang)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Index untuk performa
CREATE INDEX idx_barang_masuk_tanggal ON barang_masuk(tanggal);
CREATE INDEX idx_barang_masuk_supplier ON barang_masuk(id_supplier);
CREATE INDEX idx_barang_masuk_jenis ON barang_masuk(jenis);
CREATE INDEX idx_barang_masuk_status ON barang_masuk(status);
CREATE INDEX idx_barang_masuk_nomor ON barang_masuk(nomor_penerimaan);

CREATE INDEX idx_barang_masuk_detail_barang_masuk ON barang_masuk_detail(id_barang_masuk);
CREATE INDEX idx_barang_masuk_detail_barang ON barang_masuk_detail(id_barang);
CREATE INDEX idx_barang_masuk_detail_gudang ON barang_masuk_detail(id_gudang);

-- Trigger untuk update summary di header saat detail berubah
DELIMITER $$

CREATE TRIGGER trg_update_barang_masuk_summary_insert
AFTER INSERT ON barang_masuk_detail
FOR EACH ROW
BEGIN
    UPDATE barang_masuk SET
        total_item = (
            SELECT COUNT(*) FROM barang_masuk_detail 
            WHERE id_barang_masuk = NEW.id_barang_masuk
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_diterima), 0) FROM barang_masuk_detail 
            WHERE id_barang_masuk = NEW.id_barang_masuk
        )
    WHERE id = NEW.id_barang_masuk;
END$$

CREATE TRIGGER trg_update_barang_masuk_summary_update
AFTER UPDATE ON barang_masuk_detail
FOR EACH ROW
BEGIN
    UPDATE barang_masuk SET
        total_item = (
            SELECT COUNT(*) FROM barang_masuk_detail 
            WHERE id_barang_masuk = NEW.id_barang_masuk
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_diterima), 0) FROM barang_masuk_detail 
            WHERE id_barang_masuk = NEW.id_barang_masuk
        )
    WHERE id = NEW.id_barang_masuk;
END$$

CREATE TRIGGER trg_update_barang_masuk_summary_delete
AFTER DELETE ON barang_masuk_detail
FOR EACH ROW
BEGIN
    UPDATE barang_masuk SET
        total_item = (
            SELECT COUNT(*) FROM barang_masuk_detail 
            WHERE id_barang_masuk = OLD.id_barang_masuk
        ),
        total_qty = (
            SELECT COALESCE(SUM(qty_diterima), 0) FROM barang_masuk_detail 
            WHERE id_barang_masuk = OLD.id_barang_masuk
        )
    WHERE id = OLD.id_barang_masuk;
END$$

-- Trigger untuk finalisasi barang masuk ke stok_movement
CREATE TRIGGER trg_finalize_barang_masuk
AFTER UPDATE ON barang_masuk
FOR EACH ROW
BEGIN
    -- Hanya jalankan jika status berubah dari draft ke final
    IF OLD.status = 'draft' AND NEW.status = 'final' THEN
        -- Insert semua detail barang masuk ke stok_movement
        INSERT INTO stok_movement (
            tanggal, id_barang, id_gudang, tipe_transaksi,
            qty_in, qty_out, keterangan, ref_transaksi, user_input
        )
        SELECT 
            CONCAT(NEW.tanggal, ' ', TIME(NOW())),
            bmd.id_barang,
            bmd.id_gudang,
            CASE 
                WHEN NEW.jenis = 'pembelian' THEN 'pembelian'
                WHEN NEW.jenis = 'retur_penjualan' THEN 'retur_jual'
                WHEN NEW.jenis = 'transfer_masuk' THEN 'transfer_masuk'
                ELSE 'pembelian'
            END,
            bmd.qty_diterima,
            0,
            CONCAT('Barang Masuk - ', NEW.jenis, ': ', COALESCE(bmd.keterangan, NEW.keterangan)),
            NEW.nomor_penerimaan,
            CONCAT('user_', COALESCE(NEW.finalized_by, NEW.created_by))
        FROM barang_masuk_detail bmd
        WHERE bmd.id_barang_masuk = NEW.id
        AND bmd.qty_diterima > 0;
    END IF;
END$$

DELIMITER ;

-- View untuk laporan barang masuk
CREATE VIEW v_barang_masuk_summary AS
SELECT 
    bm.id,
    bm.nomor_penerimaan,
    bm.tanggal,
    s.kode as kode_supplier,
    s.nama as nama_supplier,
    bm.jenis,
    bm.ref_nomor,
    bm.status,
    bm.total_item,
    bm.total_qty,
    bm.keterangan,
    bm.created_by,
    bm.finalized_by,
    bm.finalized_at,
    bm.created_at
FROM barang_masuk bm
LEFT JOIN supplier s ON bm.id_supplier = s.id
ORDER BY bm.tanggal DESC, bm.id DESC;

-- View untuk detail barang masuk dengan info lengkap
CREATE VIEW v_barang_masuk_detail AS
SELECT 
    bmd.id,
    bmd.id_barang_masuk,
    bm.nomor_penerimaan,
    bm.tanggal,
    bm.status,
    bm.jenis,
    s.kode as kode_supplier,
    s.nama as nama_supplier,
    b.kode_barang,
    b.nama_barang,
    b.merk,
    b.tipe,
    g.kode_gudang,
    g.nama_gudang,
    sat.kode_satuan,
    sat.nama_satuan,
    bmd.qty_diterima,
    bmd.harga_satuan,
    bmd.total_harga,
    bmd.keterangan,
    bmd.created_at
FROM barang_masuk_detail bmd
JOIN barang_masuk bm ON bmd.id_barang_masuk = bm.id
LEFT JOIN supplier s ON bm.id_supplier = s.id
JOIN barang b ON bmd.id_barang = b.id
JOIN gudang g ON bmd.id_gudang = g.id
LEFT JOIN satuan sat ON bmd.id_satuan = sat.id
ORDER BY bm.tanggal DESC, b.nama_barang ASC;

-- Insert data contoh untuk testing
INSERT INTO barang_masuk (
    nomor_penerimaan, tanggal, id_supplier, jenis, ref_nomor, keterangan, created_by
) VALUES 
('BM-2025-001', '2025-01-15', 1, 'pembelian', 'PO-2025-001', 'Pembelian barang elektronik bulan Januari', 1);

-- Insert detail contoh (akan diisi via aplikasi)
-- INSERT INTO barang_masuk_detail (id_barang_masuk, id_barang, id_gudang, qty_diterima, id_satuan, harga_satuan, keterangan) VALUES 
-- (1, 1, 4, 10.00, 1, 4500000.00, 'Smartphone Samsung Galaxy A54 5G'),
-- (1, 2, 4, 5.00, 1, 7800000.00, 'Laptop ASUS VivoBook 14');
