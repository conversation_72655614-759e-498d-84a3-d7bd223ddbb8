# Dokumentasi Modul Barang Keluar

## Deskripsi
Modul Barang Keluar adalah sistem untuk mengelola pengeluaran barang untuk berbagai keperluan seperti penjualan, retur pembelian, transfer keluar, dll. Modul ini terintegrasi penuh dengan sistem stok management untuk otomatisasi update stok setelah finalisasi dengan validasi ketersediaan stok.

## Struktur Database

### 1. Tabel barang_keluar (Header)
```sql
CREATE TABLE barang_keluar (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    nomor_pengeluaran VARCHAR(50) NOT NULL UNIQUE,
    tanggal DATE NOT NULL,
    id_pelanggan INT,
    jenis <PERSON>('penjualan', 'retur_pembelian', 'transfer_keluar', 'penyesuaian', 'produksi', 'rusak', 'hilang', 'sample') NOT NULL,
    ref_nomor VARCHAR(50),
    keterangan TEXT,
    status ENUM('draft', 'final') DEFAULT 'draft',
    total_item INT DEFAULT 0,
    total_qty DECIMAL(15,2) DEFAULT 0,
    created_by INT,
    finalized_by INT,
    finalized_at DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_pelanggan) REFERENCES pelanggan(id)
);
```

### 2. Tabel barang_keluar_detail (Detail)
```sql
CREATE TABLE barang_keluar_detail (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    id_barang_keluar BIGINT UNSIGNED NOT NULL,
    id_barang INT NOT NULL,
    id_gudang INT NOT NULL,
    qty_keluar DECIMAL(12,2) NOT NULL DEFAULT 0,
    id_satuan INT,
    harga_satuan DECIMAL(15,2) DEFAULT 0,
    total_harga DECIMAL(15,2) GENERATED ALWAYS AS (qty_keluar * harga_satuan) STORED,
    keterangan TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_barang_keluar) REFERENCES barang_keluar(id) ON DELETE CASCADE,
    FOREIGN KEY (id_barang) REFERENCES barang(id),
    FOREIGN KEY (id_gudang) REFERENCES gudang(id),
    FOREIGN KEY (id_satuan) REFERENCES satuan(id)
);
```

### 3. Trigger Database
- **trg_update_barang_keluar_summary_***: Update summary di header saat detail berubah
- **trg_finalize_barang_keluar**: Auto insert ke stok_movement saat status berubah ke final

### 4. Views
- **v_barang_keluar_summary**: Summary barang keluar dengan info pelanggan
- **v_barang_keluar_detail**: Detail barang keluar dengan info lengkap

## Jenis Pengeluaran Barang

| Jenis | Deskripsi | Pelanggan Required | Typical Use Case |
|-------|-----------|-------------------|------------------|
| **penjualan** | Penjualan ke customer | ✅ | Sales Order |
| **retur_pembelian** | Retur ke supplier | ❌ | Return ke supplier |
| **transfer_keluar** | Transfer antar gudang | ❌ | Inter-warehouse transfer |
| **penyesuaian** | Penyesuaian stok | ❌ | Stock adjustment |
| **produksi** | Untuk keperluan produksi | ❌ | Manufacturing input |
| **rusak** | Barang rusak | ❌ | Damaged goods |
| **hilang** | Barang hilang | ❌ | Lost items |
| **sample** | Sample produk | ❌ | Product samples |

## File yang Dibuat

### 1. Database
- `DB/create_barang_keluar.sql` - Script tabel, trigger, dan view
- `DB/add_menu_barang_keluar.sql` - Script menu

### 2. Backend (MVC)
- `application/models/Mod_barang_keluar.php` - Model untuk CRUD dan business logic
- `application/controllers/BarangKeluar.php` - Controller untuk handling request
- `application/views/barang_keluar/barang_keluar.php` - List view
- `application/views/barang_keluar/form_input.php` - Form input header
- `application/views/barang_keluar/detail_barang_keluar.php` - Detail management

## Fitur Utama

### 1. Header Management
- Generate nomor pengeluaran otomatis (Format: BK-YYYY-MM-XXX)
- Multi-tab form input (Data Dasar, Pelanggan & Referensi, Keterangan)
- Draft/Final status workflow
- Validasi nomor unik

### 2. Detail Management
- Add/Edit/Delete detail items
- Real-time stock validation
- Auto-populate satuan dan harga dari master barang
- Duplicate item validation (barang + gudang yang sama)
- Calculated total harga (qty × harga satuan)

### 3. Stock Integration
- Real-time stock availability check
- Comprehensive stock validation saat finalisasi
- Auto-update stok_movement saat finalisasi
- Visual stock indicator (merah jika stok tidak cukup)

### 4. Finalisasi Process
- Validasi detail items tidak boleh kosong
- Validasi stok tersedia untuk semua items
- Auto-insert ke stok_movement dengan mapping jenis transaksi
- Lock data setelah finalisasi (tidak bisa edit/delete)

### Integration with Stok Movement
Saat finalisasi, sistem otomatis:
- Insert ke stok_movement dengan tipe sesuai jenis
- qty_out = qty_keluar
- Referensi ke nomor pengeluaran
- Mapping jenis: penjualan→penjualan, retur_pembelian→retur_beli, transfer_keluar→transfer_keluar, lainnya→penyesuaian

## Method di Model

### Mod_barang_keluar.php

#### Header Methods
```php
// CRUD header
function insert($data)
function update($id, $data)
function get($id)
function delete($id)

// Generate nomor otomatis
function generate_nomor()
function check_nomor_exists($nomor, $id = null)

// Finalisasi
function finalize_barang_keluar($id, $user_final)
function validate_stock_availability($id_barang_keluar)

// Dropdown data
function get_pelanggan_dropdown()
function get_barang_dropdown()
function get_gudang_dropdown()
function get_satuan_dropdown()
```

#### Detail Methods
```php
// CRUD detail
function insert_detail($data)
function update_detail($id, $data)
function get_detail($id_barang_keluar)
function get_detail_by_id($id)
function delete_detail($id)

// Helper methods
function get_barang_detail($id_barang)
function get_stok_barang($id_barang, $id_gudang)
function check_duplicate_detail($id_barang_keluar, $id_barang, $id_gudang, $id_detail = null)
```

## Validasi

### Header Validation
- Nomor pengeluaran: Format BK-YYYY-MM-XXX, unique
- Tanggal: Required
- Jenis: Required

### Detail Validation
- Barang: Required
- Gudang: Required
- Qty keluar: Required, numeric, > 0
- Stok validation: Qty keluar tidak boleh melebihi stok tersedia
- Duplicate validation: Kombinasi barang + gudang harus unik per transaksi
- Harga satuan: Optional, numeric jika diisi

## UI Features

### 1. List View
- DataTables dengan server-side processing
- Badge untuk jenis dan status
- Action buttons berdasarkan status
- Responsive design

### 2. Form Input
- Multi-tab layout untuk better UX
- Select2 untuk dropdown
- Auto-generate nomor
- Conditional field visibility

### 3. Detail Management
- Modal form untuk add/edit detail
- Real-time stock display
- Auto-calculate total harga
- Stock validation dengan visual indicator

### 4. Notifications
- SweetAlert untuk semua konfirmasi
- Detailed error messages
- Stock insufficient warning dengan detail items

## Instalasi

1. Jalankan script database:
   ```sql
   source DB/create_barang_keluar.sql;
   source DB/add_menu_barang_keluar.sql;
   ```

2. Pastikan semua file MVC sudah di-upload ke direktori yang sesuai

3. Akses menu "Barang Keluar" di sidebar Warehouse

## Catatan Penting

- Modul ini memerlukan tabel stok_barang dan stok_movement yang sudah ada
- Finalisasi akan mengunci data dan tidak bisa diubah lagi
- Validasi stok dilakukan real-time untuk mencegah overselling
- Integrasi penuh dengan master data (barang, gudang, satuan, pelanggan)
