-- <PERSON>ript untuk menambahkan menu Barang Keluar
-- Jalankan script ini setelah tabel barang_keluar dibuat

-- Update submenu yang sudah ada untuk Barang Keluar
UPDATE tbl_submenu 
SET link = 'barangkeluar', nama_submenu = 'Barang Keluar'
WHERE id_submenu = 76 AND nama_submenu = 'Barang Keluar';

-- <PERSON><PERSON> belum ada, insert submenu baru
INSERT IGNORE INTO tbl_submenu (id_submenu, nama_submenu, link, icon, id_menu, is_active, urutan)
VALUES (76, 'Barang Keluar', 'barangkeluar', 'far fa-circle', 9, 'Y', 2);

-- Insert akses menu untuk semua level user yang ada
INSERT IGNORE INTO tbl_akses_submenu (id_level, id_submenu, view, add, edit, delete, print, upload, download)
SELECT id_level, 76, 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y' FROM tbl_userlevel;

-- <PERSON><PERSON><PERSON><PERSON> hasil
SELECT 'Menu Barang Keluar berhasil ditambahkan/diupdate' as status;
SELECT * FROM tbl_submenu WHERE id_submenu = 76;
